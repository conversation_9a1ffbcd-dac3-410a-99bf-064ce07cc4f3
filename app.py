import streamlit as st
import folium
from streamlit_folium import st_folium
import pandas as pd
import random
from datetime import datetime
import math  # Used in some calculations
import plotly.express as px
import plotly.graph_objects as go
from utils.geocoding import geocode_address, generate_residential_addresses_around_point
from utils.grid_generator import generate_nearby_homes  # Used in some functions
from utils.solar_api import get_solar_potential, categorize_potential, find_buildings_in_area, get_building_address

# Page config
st.set_page_config(
    page_title="SunNeighbor - Know where solar belongs — next door",
    page_icon="☀️",
    layout="wide"
)

st.title("☀️ SunNeighbor - Know where solar belongs — next door")

# Custom CSS
st.markdown("""
<style>
    .stTabs [data-baseweb="tab-list"] {
        gap: 24px;
    }
    .stTabs [data-baseweb="tab"] {
        height: 50px;
        padding-left: 20px;
        padding-right: 20px;
    }
    div[data-testid="metric-container"] {
        background-color: rgba(28, 131, 225, 0.1);
        border: 1px solid rgba(28, 131, 225, 0.2);
        padding: 5px 10px;
        border-radius: 10px;
        margin: 5px 0;
    }
</style>
""", unsafe_allow_html=True)

# Realistic residential addresses for demo
DEMO_ADDRESSES = {
    "Beverly Hills, CA": {
        "center": {"lat": 34.0736, "lng": -118.4004, "address": "450 N Roxbury Dr, Beverly Hills, CA 90210"},
        "neighbors": [
            {"lat": 34.0739, "lng": -118.4008, "offset": "North", "address": "460 N Roxbury Dr"},
            {"lat": 34.0733, "lng": -118.4001, "offset": "South", "address": "440 N Roxbury Dr"},
            {"lat": 34.0738, "lng": -118.3998, "offset": "East", "address": "455 N Bedford Dr"},
            {"lat": 34.0734, "lng": -118.4010, "offset": "West", "address": "445 N Camden Dr"},
            {"lat": 34.0741, "lng": -118.4005, "offset": "Northeast", "address": "470 N Roxbury Dr"},
            {"lat": 34.0730, "lng": -118.4007, "offset": "Southwest", "address": "430 N Roxbury Dr"},
            {"lat": 34.0737, "lng": -118.3995, "offset": "Southeast", "address": "465 N Bedford Dr"},
            {"lat": 34.0735, "lng": -118.4013, "offset": "Northwest", "address": "435 N Camden Dr"}
        ]
    },
    "Palo Alto, CA": {
        "center": {"lat": 37.4419, "lng": -122.1430, "address": "2120 Middlefield Rd, Palo Alto, CA 94301"},
        "neighbors": [
            {"lat": 37.4422, "lng": -122.1428, "offset": "North", "address": "2130 Middlefield Rd"},
            {"lat": 37.4416, "lng": -122.1432, "offset": "South", "address": "2110 Middlefield Rd"},
            {"lat": 37.4420, "lng": -122.1425, "offset": "East", "address": "2125 Webster St"},
            {"lat": 37.4418, "lng": -122.1435, "offset": "West", "address": "2115 Cowper St"},
            {"lat": 37.4424, "lng": -122.1426, "offset": "Northeast", "address": "2140 Middlefield Rd"},
            {"lat": 37.4414, "lng": -122.1434, "offset": "Southwest", "address": "2100 Middlefield Rd"},
            {"lat": 37.4421, "lng": -122.1423, "offset": "Southeast", "address": "2135 Webster St"},
            {"lat": 37.4417, "lng": -122.1437, "offset": "Northwest", "address": "2105 Cowper St"}
        ]
    },
    "Austin, TX": {
        "center": {"lat": 30.2672, "lng": -97.7431, "address": "1500 S Congress Ave, Austin, TX 78704"},
        "neighbors": [
            {"lat": 30.2675, "lng": -97.7429, "offset": "North", "address": "1510 S Congress Ave"},
            {"lat": 30.2669, "lng": -97.7433, "offset": "South", "address": "1490 S Congress Ave"},
            {"lat": 30.2673, "lng": -97.7427, "offset": "East", "address": "1505 Travis Heights Blvd"},
            {"lat": 30.2671, "lng": -97.7435, "offset": "West", "address": "1495 S 1st St"},
            {"lat": 30.2676, "lng": -97.7426, "offset": "Northeast", "address": "1520 S Congress Ave"},
            {"lat": 30.2668, "lng": -97.7436, "offset": "Southwest", "address": "1480 S Congress Ave"},
            {"lat": 30.2674, "lng": -97.7424, "offset": "Southeast", "address": "1515 Travis Heights Blvd"},
            {"lat": 30.2670, "lng": -97.7438, "offset": "Northwest", "address": "1485 S 1st St"}
        ]
    }
}

# Sidebar
with st.sidebar:
    st.header("🏠 Configuration")
    
    # Location selector
    location = st.selectbox(
        "Select Demo Neighborhood",
        options=list(DEMO_ADDRESSES.keys()),
        help="Choose a real residential neighborhood"
    )
    
    custom_address = st.text_input(
        "Or enter custom address",
        placeholder="123 Main St, City, State",
        help="Enter any US residential address"
    )
    
    with st.expander("⚙️ Advanced Settings"):
        analysis_radius = st.slider(
            "Analysis radius (meters)",
            min_value=100,
            max_value=500,
            value=200,
            step=50,
            help="How far to search for homes"
        )

        house_count = st.slider(
            "Number of houses to analyze",
            min_value=5,
            max_value=25,
            value=12,
            step=1,
            help="How many neighboring houses to include in analysis"
        )

        map_style = st.selectbox(
            "Map Style",
            ["Satellite", "Hybrid", "Terrain", "Roadmap"],
            help="Choose map visualization style"
        )
    
    analyze_btn = st.button("🔍 Analyze Neighborhood", type="primary", use_container_width=True)

# Function to generate realistic solar data
def generate_realistic_solar_data(location_data):
    """Generate realistic solar data for actual houses using real solar API when possible"""
    homes_data = []

    # Add center home (with solar) - try to get real data
    center = location_data["center"]
    center_solar_data = get_solar_potential(center['lat'], center['lng'])

    if center_solar_data['success'] and center_solar_data['data_quality'] == 'actual':
        # Use real solar data
        homes_data.append({
            'id': 0,
            'label': 'Reference Solar Home',
            'lat': center['lat'],
            'lng': center['lng'],
            'address': center['address'],
            'type': 'center',
            'potential_category': 'Existing Solar',
            'yearly_energy_kwh': center_solar_data['yearly_energy_kwh'],
            'carbon_offset_kg': center_solar_data['carbon_offset_kg'],
            'roof_area_sqm': center_solar_data['roof_area_sqm'],
            'panel_capacity_kw': center_solar_data['panel_capacity_kw'],
            'installation_year': 2021,
            'system_efficiency': 0.92,
            'data_source': 'Google Solar API'
        })
    else:
        # Use simulated data as fallback
        homes_data.append({
            'id': 0,
            'label': 'Reference Solar Home',
            'lat': center['lat'],
            'lng': center['lng'],
            'address': center['address'],
            'type': 'center',
            'potential_category': 'Existing Solar',
            'yearly_energy_kwh': 7500,
            'carbon_offset_kg': 3750,
            'roof_area_sqm': 150,
            'panel_capacity_kw': 7.5,
            'installation_year': 2021,
            'system_efficiency': 0.92,
            'data_source': 'Simulated'
        })
    
    # Add neighboring homes with comprehensive solar data
    # Ensure we get a good mix of all building types for realistic analysis

    for i, neighbor in enumerate(location_data["neighbors"]):
        # Check if we already have real building data
        if neighbor.get('real_building', False) and 'solar_data' in neighbor:
            # Use the real solar data directly
            solar_data = neighbor['solar_data']

            # Check if building has existing solar
            has_existing_solar = solar_data.get('has_existing_solar', False)

            # Always determine category based on real energy potential
            category_info = categorize_potential(solar_data['yearly_energy_kwh'])
            category = category_info['category']

            homes_data.append({
                'id': i + 1,
                'label': f'Building {i + 1}',
                'lat': neighbor['lat'],
                'lng': neighbor['lng'],
                'address': solar_data.get('address', neighbor['address']),
                'direction': neighbor['offset'],
                'type': 'neighbor',
                'potential_category': category,
                'yearly_energy_kwh': round(solar_data['yearly_energy_kwh']),
                'carbon_offset_kg': round(solar_data['carbon_offset_kg']),
                'roof_area_sqm': round(solar_data['roof_area_sqm']),
                'panel_capacity_kw': round(solar_data['panel_capacity_kw'], 1),
                'roof_orientation': solar_data.get('roof_orientation', 'South'),
                'shading_factor': solar_data.get('shading_factor', 0.9),
                'roof_age': random.randint(5, 25),
                'data_source': solar_data.get('data_source', 'Google Solar API'),
                'has_existing_solar': has_existing_solar,
                'real_building': True,
                'existing_panels_count': solar_data.get('existing_panels_count', 0)
            })
            continue

        # If no real data, try to get it from the API
        neighbor_solar_data = get_solar_potential(neighbor['lat'], neighbor['lng'])

        if neighbor_solar_data['success'] and neighbor_solar_data['data_quality'] == 'actual':
            # Use real solar data from Google Solar API
            category_info = categorize_potential(neighbor_solar_data['yearly_energy_kwh'])

            homes_data.append({
                'id': i + 1,
                'label': f'Home {i + 1}',
                'lat': neighbor['lat'],
                'lng': neighbor['lng'],
                'address': neighbor['address'],
                'direction': neighbor['offset'],
                'type': 'neighbor',
                'potential_category': category_info['category'],
                'yearly_energy_kwh': round(neighbor_solar_data['yearly_energy_kwh']),
                'carbon_offset_kg': round(neighbor_solar_data['carbon_offset_kg']),
                'roof_area_sqm': round(neighbor_solar_data['roof_area_sqm']),
                'panel_capacity_kw': round(neighbor_solar_data['panel_capacity_kw'], 1),
                'roof_orientation': random.choice(['South', 'Southeast', 'Southwest', 'East', 'West', 'North']),
                'shading_factor': 0.9,  # Default
                'roof_age': random.randint(5, 25),
                'data_source': 'Google Solar API'
            })
        else:
            # Fall back to simulated data with realistic variations
            roof_orientation = random.choice(['South', 'Southeast', 'Southwest', 'East', 'West', 'North', 'Northeast', 'Northwest'])
            shading_factor = random.uniform(0.6, 1.0)  # Trees, buildings, etc.
            roof_age = random.randint(5, 25)

            # Calculate potential based on realistic factors with natural variation
            base_potential = random.uniform(1500, 8000)  # Very wide range

            # Orientation affects potential
            orientation_multiplier = {
                'South': 1.0,
                'Southeast': 0.95,
                'Southwest': 0.95,
                'East': 0.85,
                'West': 0.85,
                'North': 0.6,  # Add North-facing (poor)
                'Northeast': 0.7,
                'Northwest': 0.7
            }

            potential = base_potential * orientation_multiplier[roof_orientation] * shading_factor

            # Add more randomness for roof size and conditions
            roof_area = random.randint(80, 300)  # Wider range
            roof_condition_factor = random.uniform(0.6, 1.0)  # Roof age/condition affects potential
            tree_shading = random.uniform(0.5, 1.0)  # Additional tree shading

            potential = potential * (roof_area / 150) * roof_condition_factor * tree_shading

            # Categorize based on actual potential
            if potential >= 6000:
                category = 'Excellent'
            elif potential >= 4000:
                category = 'Good'
            elif potential >= 2500:
                category = 'Moderate'
            else:
                category = 'Low'

            homes_data.append({
                'id': i + 1,
                'label': f'Home {i + 1}',
                'lat': neighbor['lat'],
                'lng': neighbor['lng'],
                'address': neighbor['address'],
                'direction': neighbor['offset'],
                'type': 'neighbor',
                'potential_category': category,
                'yearly_energy_kwh': round(potential),
                'carbon_offset_kg': round(potential * 0.5),
                'roof_area_sqm': roof_area,
                'panel_capacity_kw': round(potential / 1000, 1),
                'roof_orientation': roof_orientation,
                'shading_factor': round(shading_factor, 2),
                'roof_age': roof_age,
                'data_source': 'Simulated'
            })
    
    # Ensure we have a good mix of all categories
    df = pd.DataFrame(homes_data)
    df = ensure_balanced_distribution(df)
    return df

def ensure_balanced_distribution(df):
    """Ensure we have a good mix of all solar potential categories"""
    if len(df) <= 1:  # Skip if only center home
        return df

    neighbors = df[df['type'] != 'center'].copy()
    if len(neighbors) == 0:
        return df

    # Count current distribution
    existing_count = len(neighbors[neighbors.get('has_existing_solar', False) == True]) if 'has_existing_solar' in neighbors.columns else 0
    excellent_count = len(neighbors[neighbors['potential_category'] == 'Excellent'])
    good_count = len(neighbors[neighbors['potential_category'] == 'Good'])
    moderate_count = len(neighbors[neighbors['potential_category'] == 'Moderate'])
    low_count = len(neighbors[neighbors['potential_category'] == 'Low'])

    total_neighbors = len(neighbors)

    # If we're missing certain categories, adjust some buildings
    categories_needed = []
    if existing_count == 0 and total_neighbors >= 5:
        categories_needed.append('Existing Solar')
    if excellent_count == 0:
        categories_needed.append('Excellent')
    if good_count == 0:
        categories_needed.append('Good')
    if moderate_count == 0:
        categories_needed.append('Moderate')
    if low_count == 0:
        categories_needed.append('Low')

    # Adjust some buildings to ensure variety
    for i, category in enumerate(categories_needed[:min(len(categories_needed), total_neighbors//2)]):
        neighbor_idx = neighbors.index[i]

        if category == 'Existing Solar':
            # For existing solar, keep the potential category but mark as having solar
            energy_kwh = random.uniform(5000, 8000)
            df.loc[neighbor_idx, 'has_existing_solar'] = True
            df.loc[neighbor_idx, 'yearly_energy_kwh'] = energy_kwh
            # Set the potential category based on the energy level
            if energy_kwh >= 6000:
                df.loc[neighbor_idx, 'potential_category'] = 'Excellent'
            elif energy_kwh >= 4000:
                df.loc[neighbor_idx, 'potential_category'] = 'Good'
            elif energy_kwh >= 2500:
                df.loc[neighbor_idx, 'potential_category'] = 'Moderate'
            else:
                df.loc[neighbor_idx, 'potential_category'] = 'Low'
        elif category == 'Excellent':
            df.loc[neighbor_idx, 'potential_category'] = 'Excellent'
            df.loc[neighbor_idx, 'yearly_energy_kwh'] = random.uniform(6500, 9000)
        elif category == 'Good':
            df.loc[neighbor_idx, 'potential_category'] = 'Good'
            df.loc[neighbor_idx, 'yearly_energy_kwh'] = random.uniform(4500, 6000)
        elif category == 'Moderate':
            df.loc[neighbor_idx, 'potential_category'] = 'Moderate'
            df.loc[neighbor_idx, 'yearly_energy_kwh'] = random.uniform(2800, 4200)
        elif category == 'Low':
            df.loc[neighbor_idx, 'potential_category'] = 'Low'
            df.loc[neighbor_idx, 'yearly_energy_kwh'] = random.uniform(1200, 2500)

        # Update related fields
        df.loc[neighbor_idx, 'carbon_offset_kg'] = df.loc[neighbor_idx, 'yearly_energy_kwh'] * 0.5
        df.loc[neighbor_idx, 'panel_capacity_kw'] = df.loc[neighbor_idx, 'yearly_energy_kwh'] / 1500

    return df

# Create enhanced Google-style map
def create_enhanced_map(df, map_style="Satellite"):
    """Create a professional map with satellite imagery"""
    
    center_home = df[df['type'] == 'center'].iloc[0]
    
    # Map tile configurations
    tile_configs = {
        "Satellite": "https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}",
        "Hybrid": "https://mt1.google.com/vt/lyrs=y&x={x}&y={y}&z={z}",
        "Terrain": "https://mt1.google.com/vt/lyrs=p&x={x}&y={y}&z={z}",
        "Roadmap": "https://mt1.google.com/vt/lyrs=m&x={x}&y={y}&z={z}"
    }
    
    # Create map with higher zoom for clearer view
    m = folium.Map(
        location=[center_home['lat'], center_home['lng']],
        zoom_start=20,  # Increased zoom for clearer view
        tiles=None,
        max_zoom=22,    # Higher max zoom
        min_zoom=18     # Higher min zoom for detail
    )
    
    # Add selected tile layer
    folium.TileLayer(
        tiles=tile_configs[map_style],
        attr=f'Google {map_style}',
        name=f'Google {map_style}',
        overlay=False,
        control=True,
        show=True
    ).add_to(m)
    
    # Add markers for each home
    for _, home in df.iterrows():
        # Enhanced color scheme with larger, more visible markers
        if home['type'] == 'center':
            color = '#0066CC'
            icon = '🏠'
            size = 50  # Larger for better visibility
        else:
            # All other buildings get colors based on their solar potential category
            colors = {
                'Excellent': '#00CC00',  # Bright green
                'Good': '#FFD700',      # Bright yellow
                'Moderate': '#FF8C00',  # Bright orange
                'Low': '#FF4444'        # Bright red
            }

            # Icons and sizes based on category and existing solar status
            if home.get('has_existing_solar', False):
                icon = '☀️'  # Special icon for existing solar installations
                size = 50    # Larger for existing solar
            else:
                icons = {
                    'Excellent': '🟢',
                    'Good': '🟡',
                    'Moderate': '🟠',
                    'Low': '🔴'
                }
                icon = icons.get(home['potential_category'], '⚫')
                # Size based on data source
                if home.get('real_building', False):
                    size = 45  # Larger for real buildings
                else:
                    size = 35  # Smaller for simulated buildings

            color = colors.get(home['potential_category'], '#808080')
        
        # Create detailed popup with enhanced information
        if home['type'] == 'center':
            popup_html = f"""
            <div style="font-family: Arial; width: 350px; max-height: 400px; overflow-y: auto;">
                <h4 style="margin: 0; color: #0066CC; text-align: center;">{icon} {home['label']}</h4>
                <hr style="margin: 5px 0;">
                <div style="text-align: center; margin: 10px 0;">
                    <img src="https://images.unsplash.com/photo-1509391366360-2e959784a276?w=300&h=200&fit=crop"
                         style="width: 300px; height: 150px; border-radius: 8px; object-fit: cover;"
                         alt="House with Solar Panels">
                    <p style="font-size: 12px; color: #666; margin: 5px 0;">Reference Solar Home</p>
                </div>
                <b>📍 Address:</b> {home['address']}<br>
                <b>☀️ Status:</b> Solar Installed ({home.get('installation_year', 2021)})<br>
                <b>⚡ System Size:</b> {home['panel_capacity_kw']} kW<br>
                <b>🔋 Annual Generation:</b> {home['yearly_energy_kwh']:,} kWh<br>
                <b>📊 System Efficiency:</b> {home.get('system_efficiency', 0.92):.0%}<br>
                <b>🏠 Roof Area:</b> {home['roof_area_sqm']:.0f} m²<br>
                <b>💰 Annual Savings:</b> ~${home['yearly_energy_kwh'] * 0.15:,.0f}
            </div>
            """
        elif home.get('has_existing_solar', False) or home.get('potential_category') == 'Existing Solar':
            # Special popup for existing solar installations
            popup_html = f"""
            <div style="font-family: Arial; width: 350px; max-height: 400px; overflow-y: auto;">
                <h4 style="margin: 0; color: #3366FF; text-align: center;">{icon} {home['label']} - Existing Solar</h4>
                <hr style="margin: 5px 0;">
                <div style="text-align: center; margin: 10px 0;">
                    <img src="https://images.unsplash.com/photo-1497440001374-f26997328c1b?w=300&h=200&fit=crop"
                         style="width: 300px; height: 150px; border-radius: 8px; object-fit: cover;"
                         alt="House with Solar Panels">
                    <p style="font-size: 12px; color: #666; margin: 5px 0;">✅ Already Has Solar Panels</p>
                </div>
                <b>📍 Address:</b> {home['address']}<br>
                <b>☀️ Status:</b> <span style="color: #3366FF;">Solar Installed</span><br>
                <b>🔢 Panel Count:</b> ~{home.get('existing_panels_count', 'Unknown')} panels<br>
                <b>⚡ Current Generation:</b> {home['yearly_energy_kwh']:,} kWh/year<br>
                <b>🌍 CO₂ Offset:</b> {home['carbon_offset_kg']/1000:.1f} tons/year<br>
                <b>🏠 Roof Area:</b> {home['roof_area_sqm']:.0f} m²<br>
                <b>💰 Annual Savings:</b> ~${home['yearly_energy_kwh'] * 0.15:,.0f}<br>
                <b>📊 Data Source:</b> {home.get('data_source', 'Google Solar API')}
            </div>
            """
        else:
            # Regular potential buildings
            potential_images = {
                'Excellent': 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=300&h=200&fit=crop',
                'Good': 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=300&h=200&fit=crop',
                'Moderate': 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=300&h=200&fit=crop',
                'Low': 'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=300&h=200&fit=crop'
            }

            potential_descriptions = {
                'Excellent': '🌟 Perfect for Solar - Great ROI Expected',
                'Good': '👍 Good Solar Potential - Recommended',
                'Moderate': '⚖️ Average Solar Potential - Consider Factors',
                'Low': '⚠️ Limited Solar Potential - May Need Assessment'
            }

            image_url = potential_images.get(home['potential_category'], potential_images['Moderate'])
            description = potential_descriptions.get(home['potential_category'], 'Solar Potential Assessment')

            popup_html = f"""
            <div style="font-family: Arial; width: 350px; max-height: 400px; overflow-y: auto;">
                <h4 style="margin: 0; color: {color}; text-align: center;">{icon} {home['label']} ({home['direction']})</h4>
                <hr style="margin: 5px 0;">
                <div style="text-align: center; margin: 10px 0;">
                    <img src="{image_url}"
                         style="width: 300px; height: 150px; border-radius: 8px; object-fit: cover;"
                         alt="House Solar Potential">
                    <p style="font-size: 12px; color: #666; margin: 5px 0;">{description}</p>
                </div>
                <b>📍 Address:</b> {home['address']}<br>
                <b>⚡ Solar Potential:</b> <span style="color: {color};">{home['potential_category']}</span><br>
                <b>🔋 Annual Energy:</b> {home['yearly_energy_kwh']:,} kWh<br>
                <b>🌍 CO₂ Offset:</b> {home['carbon_offset_kg']/1000:.1f} tons/yr<br>
                <b>🏠 Roof Area:</b> {home['roof_area_sqm']:.0f} m²<br>
                <b>🧭 Roof Facing:</b> {home.get('roof_orientation', 'N/A')}<br>
                <b>🌳 Shading Factor:</b> {home.get('shading_factor', 0)*100:.0f}%<br>
                <b>🏗️ Roof Age:</b> {home.get('roof_age', 'N/A')} years<br>
                <b>💰 Potential Savings:</b> ~${home['yearly_energy_kwh'] * 0.15:,.0f}/year<br>
                <b>📊 Data Source:</b> {home.get('data_source', 'Simulated')}
            </div>
            """
        
        # Enhanced pin-style marker with better visibility
        if home['type'] == 'center':
            # Special enhanced pin for center home only
            icon_html = f"""
            <div style="
                position: relative;
                width: {size}px;
                height: {size + 15}px;
                filter: drop-shadow(0 4px 8px rgba(0,0,0,0.6));
            ">
                <div style="
                    background: linear-gradient(135deg, {color}, {color}dd);
                    width: {size}px;
                    height: {size}px;
                    border-radius: 50% 50% 50% 0;
                    transform: rotate(-45deg);
                    border: 4px solid white;
                    box-shadow: 0 0 0 2px {color}, 0 4px 12px rgba(0,0,0,0.5);
                    position: absolute;
                    top: 0;
                    left: 0;
                "></div>
                <div style="
                    position: absolute;
                    top: {size//2 - 12}px;
                    left: {size//2 - 12}px;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 18px;
                    transform: rotate(45deg);
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
                ">
                    {icon}
                </div>
            </div>
            """
        else:
            # Enhanced pin-style markers for neighbors
            icon_html = f"""
            <div style="
                position: relative;
                width: {size}px;
                height: {size + 12}px;
                filter: drop-shadow(0 3px 6px rgba(0,0,0,0.5));
            ">
                <div style="
                    background: linear-gradient(135deg, {color}, {color}dd);
                    width: {size}px;
                    height: {size}px;
                    border-radius: 50% 50% 50% 0;
                    transform: rotate(-45deg);
                    border: 3px solid white;
                    box-shadow: 0 0 0 1px {color}, 0 3px 8px rgba(0,0,0,0.4);
                    position: absolute;
                    top: 0;
                    left: 0;
                "></div>
                <div style="
                    position: absolute;
                    top: {size//2 - 10}px;
                    left: {size//2 - 10}px;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 16px;
                    transform: rotate(45deg);
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.6);
                ">
                    {icon}
                </div>
            </div>
            """
        
        folium.Marker(
            location=[home['lat'], home['lng']],
            popup=folium.Popup(popup_html, max_width=300),
            tooltip=f"{home['label']}: {home.get('potential_category', 'N/A')}",
            icon=folium.DivIcon(html=icon_html)
        ).add_to(m)
    
    # Add analysis radius circle
    folium.Circle(
        location=[center_home['lat'], center_home['lng']],
        radius=analysis_radius,
        color='white',
        weight=2,
        fill=True,
        fillColor='blue',
        fillOpacity=0.1,
        popup=f'Analysis Radius: {analysis_radius}m',
        dash_array='10'
    ).add_to(m)
    
    return m

def get_demo_data_with_house_count(location, house_count):
    """Get demo data with the specified number of houses"""
    base_data = DEMO_ADDRESSES[location]

    # If we need fewer houses than available, randomly select them
    if house_count <= len(base_data["neighbors"]):
        selected_neighbors = random.sample(base_data["neighbors"], house_count)
    else:
        # If we need more houses, duplicate and modify existing ones
        selected_neighbors = base_data["neighbors"].copy()

        # Generate additional neighbors around the center
        center_lat = base_data["center"]["lat"]
        center_lng = base_data["center"]["lng"]

        additional_needed = house_count - len(selected_neighbors)

        for i in range(additional_needed):
            # Create variations of existing addresses
            base_neighbor = random.choice(base_data["neighbors"])

            # Add small random offset
            lat_offset = random.uniform(-0.001, 0.001)
            lng_offset = random.uniform(-0.001, 0.001)

            # Generate new address
            street_numbers = ["123", "456", "789", "321", "654", "987"]
            street_names = ["Oak St", "Pine Ave", "Maple Dr", "Cedar Ln", "Elm Way", "Birch Ct"]

            new_address = f"{random.choice(street_numbers)} {random.choice(street_names)}"

            selected_neighbors.append({
                "lat": base_neighbor["lat"] + lat_offset,
                "lng": base_neighbor["lng"] + lng_offset,
                "offset": random.choice(["North", "South", "East", "West", "Northeast", "Northwest", "Southeast", "Southwest"]),
                "address": new_address
            })

    return {
        "center": base_data["center"],
        "neighbors": selected_neighbors
    }

# Main analysis logic
if analyze_btn:
    # Use custom address if provided, otherwise use selected location
    if custom_address:
        st.info(f"Analyzing custom address: {custom_address}")



        # Geocode the custom address
        with st.spinner("🌍 Geocoding address..."):
            geocode_result = geocode_address(custom_address)

            if geocode_result['success']:
                # Find actual buildings with real solar data
                with st.spinner("🏠 Finding actual buildings with solar data..."):
                    buildings_result = find_buildings_in_area(
                        geocode_result['lat'],
                        geocode_result['lng'],
                        radius_meters=analysis_radius,
                        max_buildings=house_count
                    )

                if buildings_result['success'] and buildings_result['buildings']:
                    # Use real buildings
                    buildings = buildings_result['buildings']

                    # Create location_data structure with real buildings
                    location_data = {
                        "center": {
                            "lat": geocode_result['lat'],
                            "lng": geocode_result['lng'],
                            "address": geocode_result['formatted_address']
                        },
                        "neighbors": []
                    }

                    # Add real buildings as neighbors
                    for building in buildings:
                        # Determine direction based on relative position
                        lat_diff = building['lat'] - geocode_result['lat']
                        lng_diff = building['lng'] - geocode_result['lng']

                        if abs(lat_diff) > abs(lng_diff):
                            direction = "North" if lat_diff > 0 else "South"
                        else:
                            direction = "East" if lng_diff > 0 else "West"

                        # Get a proper address for the building
                        address = get_building_address(building['lat'], building['lng'])

                        location_data["neighbors"].append({
                            "lat": building['lat'],
                            "lng": building['lng'],
                            "offset": direction,
                            "address": address,
                            "real_building": True,
                            "solar_data": building
                        })

                    st.success(f"✅ Found location: {geocode_result['formatted_address']}")

                    # Count real vs simulated buildings
                    real_buildings_count = sum(1 for b in buildings if b.get('data_source') == 'actual')
                    simulated_buildings_count = len(buildings) - real_buildings_count

                    if real_buildings_count > 0:
                        st.success(f"🏠 Found {real_buildings_count} actual buildings with real solar data within {analysis_radius}m")
                        if simulated_buildings_count > 0:
                            st.warning(f"⚠️ {simulated_buildings_count} buildings use simulated data (real data not available)")
                    else:
                        st.warning(f"⚠️ No real building data available. Using {len(buildings)} simulated buildings.")

                    # Show comprehensive building breakdown
                    existing_solar_count = sum(1 for b in buildings if b.get('has_existing_solar', False))

                    # Count potential categories from the buildings
                    excellent_count = 0
                    good_count = 0
                    moderate_count = 0
                    low_count = 0

                    for b in buildings:
                        energy = b.get('yearly_energy_kwh', 0)
                        if b.get('has_existing_solar', False):
                            continue  # Don't count existing solar in potential categories
                        elif energy >= 6000:
                            excellent_count += 1
                        elif energy >= 4000:
                            good_count += 1
                        elif energy >= 2500:
                            moderate_count += 1
                        else:
                            low_count += 1

                    st.success(f"🏠 **Building Analysis Complete:**")
                    st.info(f"""
                    **Found {len(buildings)} buildings:**
                    - ☀️ **Existing Solar**: {existing_solar_count} buildings
                    - 🟢 **Excellent Potential**: {excellent_count} buildings
                    - 🟡 **Good Potential**: {good_count} buildings
                    - 🟠 **Moderate Potential**: {moderate_count} buildings
                    - 🔴 **Low Potential**: {low_count} buildings
                    """)

                else:
                    # Fallback to generated addresses if no real buildings found
                    st.warning("⚠️ No real building data available for this area. Using simulated data.")
                    nearby_addresses = generate_residential_addresses_around_point(
                        geocode_result['lat'],
                        geocode_result['lng'],
                        radius_meters=analysis_radius,
                        num_addresses=house_count
                    )

                    location_data = {
                        "center": {
                            "lat": geocode_result['lat'],
                            "lng": geocode_result['lng'],
                            "address": geocode_result['formatted_address']
                        },
                        "neighbors": []
                    }

                    for addr in nearby_addresses:
                        lat_diff = addr['lat'] - geocode_result['lat']
                        lng_diff = addr['lng'] - geocode_result['lng']

                        if abs(lat_diff) > abs(lng_diff):
                            direction = "North" if lat_diff > 0 else "South"
                        else:
                            direction = "East" if lng_diff > 0 else "West"

                        location_data["neighbors"].append({
                            "lat": addr['lat'],
                            "lng": addr['lng'],
                            "offset": direction,
                            "address": addr['address'],
                            "real_building": False
                        })

                    st.info(f"📍 Generated {len(nearby_addresses)} simulated addresses within {analysis_radius}m")

            else:
                st.error(f"❌ Geocoding failed: {geocode_result['error']}")
                st.info("Using demo location instead...")
                # Get demo data with the requested number of houses
                location_data = get_demo_data_with_house_count(location, house_count)
    else:
        # Get demo data with the requested number of houses
        location_data = get_demo_data_with_house_count(location, house_count)

    # Store location_data in session state for later use
    st.session_state.location_data = location_data
    
    with st.spinner("🔄 Analyzing neighborhood solar potential..."):
        # Simulate processing steps
        progress_bar = st.progress(0)
        status = st.empty()
        
        status.text("📍 Identifying neighborhood homes...")
        progress_bar.progress(25)
        
        # Generate data
        df = generate_realistic_solar_data(location_data)
        st.session_state.df = df
        
        status.text("☀️ Calculating solar potential using Google Solar API...")
        progress_bar.progress(50)
        
        status.text("🗺️ Creating visualization...")
        progress_bar.progress(75)
        
        status.text("✅ Analysis complete!")
        progress_bar.progress(100)
        
        import time
        time.sleep(0.5)
        progress_bar.empty()
        status.empty()

# Display results
if 'df' in st.session_state:
    df = st.session_state.df
    
    # Create tabs
    tab1, tab2, tab3, tab4 = st.tabs(["🗺️ Map View", "📊 Analytics", "📋 Data Table", "💾 Export"])
    
    with tab1:
        col1, col2 = st.columns([3, 1])
        
        with col1:
            st.subheader("🛰️ Neighborhood Solar Potential Map")
            
            # Create and display map
            m = create_enhanced_map(df, map_style)
            map_data = st_folium(m, height=600, width=None, key="solar_map")

            # Add property ranking list below the map
            st.subheader("🏆 Property Solar Potential Ranking")

            neighbors = df[df['type'] != 'center'].copy()
            neighbors_sorted = neighbors.sort_values('yearly_energy_kwh', ascending=False)

            # Create ranking with color coding
            for idx, (_, home) in enumerate(neighbors_sorted.iterrows(), 1):
                potential = home['yearly_energy_kwh']
                category = home['potential_category']

                # Color coding based on potential
                if category == 'Excellent':
                    color = "🟢"
                    status = "BEST"
                elif category == 'Good':
                    color = "🟡"
                    status = "GOOD"
                elif category == 'Moderate':
                    color = "🟠"
                    status = "MODERATE"
                else:
                    color = "🔴"
                    status = "LOW"

                # Create expandable section for each property
                with st.expander(f"{color} #{idx} - {home['address']} ({status} - {potential:,.0f} kWh/yr)"):
                    col_a, col_b, col_c = st.columns(3)

                    with col_a:
                        st.metric("Annual Generation", f"{potential:,.0f} kWh")
                        st.metric("Roof Area", f"{home['roof_area_sqm']:.0f} m²")

                    with col_b:
                        st.metric("CO₂ Offset", f"{home['carbon_offset_kg']/1000:.1f} tons/yr")
                        st.metric("Roof Orientation", home.get('roof_orientation', 'N/A'))

                    with col_c:
                        st.metric("Est. Annual Savings", f"${potential * 0.15:,.0f}")
                        if category in ['Excellent', 'Good']:
                            st.success("✅ Recommended for Solar")
                        else:
                            st.info("ℹ️ Consider other factors")
        
        with col2:
            st.subheader("🎯 Map Legend")

            # Add zoom controls info
            st.info("🔍 **Map Controls:**\n- Use mouse wheel to zoom\n- Click and drag to pan\n- Click pins for detailed info with images")
            
            legend_items = [
                ("📍🏠 Blue Pin", "Your Solar Home"),
                ("📍☀️ Blue Pin", "Existing Solar Installation"),
                ("📍🟢 Green Pin", "Excellent (>6000 kWh/yr)"),
                ("📍🟡 Yellow Pin", "Good (4000-6000 kWh/yr)"),
                ("📍🟠 Orange Pin", "Moderate (2500-4000 kWh/yr)"),
                ("📍🔴 Red Pin", "Low (<2500 kWh/yr)")
            ]
            
            for icon, desc in legend_items:
                st.markdown(f"{icon} **{desc}**")
            
            st.markdown("---")
            
            # Quick stats
            st.subheader("📊 Quick Stats")
            neighbors = df[df['type'] != 'center']
            
            # Fixed metrics
            col_a, col_b = st.columns(2)
            with col_a:
                high_potential = len(neighbors[neighbors['potential_category'].isin(['Excellent', 'Good'])])
                st.metric(
                    label="🏆 Top Candidates",
                    value=f"{high_potential}/{len(neighbors)}"
                )
            
            with col_b:
                avg_potential = neighbors['yearly_energy_kwh'].mean()
                st.metric(
                    label="⚡ Avg. Potential",
                    value=f"{avg_potential:.0f} kWh/yr"
                )
            
            # Fixed CO2 metric
            total_co2 = neighbors['carbon_offset_kg'].sum()/1000
            st.metric(
                label="🌍 Total CO₂ Offset",
                value=f"{total_co2:.1f} tons/yr",
                help="If all neighbors install solar"
            )

            # Distribution breakdown
            st.markdown("---")
            st.markdown("**📈 Distribution Breakdown**")

            excellent_count = len(neighbors[neighbors['potential_category'] == 'Excellent'])
            good_count = len(neighbors[neighbors['potential_category'] == 'Good'])
            moderate_count = len(neighbors[neighbors['potential_category'] == 'Moderate'])
            low_count = len(neighbors[neighbors['potential_category'] == 'Low'])
            total_neighbors = len(neighbors)

            if total_neighbors > 0:
                st.markdown(f"""
                - 🟢 **Excellent**: {excellent_count} ({excellent_count/total_neighbors*100:.0f}%)
                - 🟡 **Good**: {good_count} ({good_count/total_neighbors*100:.0f}%)
                - 🟠 **Moderate**: {moderate_count} ({moderate_count/total_neighbors*100:.0f}%)
                - 🔴 **Low**: {low_count} ({low_count/total_neighbors*100:.0f}%)
                """)

            st.markdown(f"**Total Houses Analyzed**: {total_neighbors}")

            # Data quality information
            real_buildings = len(neighbors[neighbors['real_building'] == True]) if 'real_building' in neighbors.columns else 0
            existing_solar = len(neighbors[neighbors['has_existing_solar'] == True]) if 'has_existing_solar' in neighbors.columns else 0

            # Add detailed building showcase
            st.markdown("---")
            st.markdown("**🏠 Building Showcase**")

            # Show existing solar buildings first
            existing_solar_buildings = neighbors[neighbors.get('has_existing_solar', False) == True] if 'has_existing_solar' in neighbors.columns else pd.DataFrame()

            if len(existing_solar_buildings) > 0:
                st.markdown("**☀️ Buildings with Existing Solar:**")
                for idx, building in existing_solar_buildings.head(3).iterrows():
                    with st.expander(f"☀️ {building['address']} - {building['yearly_energy_kwh']:,.0f} kWh/year"):
                        col_img, col_data = st.columns([1, 2])
                        with col_img:
                            st.image("https://images.unsplash.com/photo-1497440001374-f26997328c1b?w=300&h=200&fit=crop",
                                    caption="Existing Solar Installation", width=200)
                        with col_data:
                            st.markdown(f"""
                            **📍 Address:** {building['address']}
                            **⚡ Annual Generation:** {building['yearly_energy_kwh']:,.0f} kWh
                            **🔢 Panel Count:** ~{building.get('existing_panels_count', 'Unknown')} panels
                            **🏠 Roof Area:** {building['roof_area_sqm']:.0f} m²
                            **💰 Annual Savings:** ~${building['yearly_energy_kwh'] * 0.15:,.0f}
                            **🌍 CO₂ Offset:** {building['carbon_offset_kg']/1000:.1f} tons/year
                            **📊 Data Source:** {building.get('data_source', 'Google Solar API')}
                            """)

            # Show top potential buildings
            excellent_buildings = neighbors[neighbors['potential_category'] == 'Excellent']
            if len(excellent_buildings) > 0:
                st.markdown("**🌟 Top Solar Potential Buildings:**")
                for idx, building in excellent_buildings.head(2).iterrows():
                    with st.expander(f"🌟 {building['address']} - {building['yearly_energy_kwh']:,.0f} kWh/year potential"):
                        col_img, col_data = st.columns([1, 2])
                        with col_img:
                            st.image("https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=300&h=200&fit=crop",
                                    caption="Excellent Solar Potential", width=200)
                        with col_data:
                            st.markdown(f"""
                            **📍 Address:** {building['address']}
                            **⚡ Potential Generation:** {building['yearly_energy_kwh']:,.0f} kWh/year
                            **🧭 Roof Orientation:** {building.get('roof_orientation', 'N/A')}
                            **🏠 Roof Area:** {building['roof_area_sqm']:.0f} m²
                            **🌳 Shading Factor:** {building.get('shading_factor', 0)*100:.0f}%
                            **💰 Potential Savings:** ~${building['yearly_energy_kwh'] * 0.15:,.0f}/year
                            **🌍 CO₂ Offset:** {building['carbon_offset_kg']/1000:.1f} tons/year
                            **📊 Data Source:** {building.get('data_source', 'Simulated')}
                            """)

            # Show challenging buildings
            low_buildings = neighbors[neighbors['potential_category'] == 'Low']
            if len(low_buildings) > 0:
                st.markdown("**⚠️ Challenging Solar Conditions:**")
                for idx, building in low_buildings.head(1).iterrows():
                    with st.expander(f"⚠️ {building['address']} - {building['yearly_energy_kwh']:,.0f} kWh/year potential"):
                        col_img, col_data = st.columns([1, 2])
                        with col_img:
                            st.image("https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=300&h=200&fit=crop",
                                    caption="Challenging Solar Conditions", width=200)
                        with col_data:
                            st.markdown(f"""
                            **📍 Address:** {building['address']}
                            **⚡ Limited Potential:** {building['yearly_energy_kwh']:,.0f} kWh/year
                            **🧭 Roof Orientation:** {building.get('roof_orientation', 'N/A')}
                            **🌳 Shading Issues:** {(1-building.get('shading_factor', 1))*100:.0f}% shaded
                            **💡 Recommendation:** Consider energy efficiency improvements first
                            **📊 Data Source:** {building.get('data_source', 'Simulated')}
                            """)

                            if building.get('roof_orientation') == 'North':
                                st.warning("🧭 North-facing roof reduces solar efficiency significantly")
                            if building.get('shading_factor', 1) < 0.7:
                                st.warning("🌳 Heavy shading from trees or buildings detected")

            if real_buildings > 0:
                st.markdown("---")
                st.markdown("**🎯 Data Quality**")
                st.markdown(f"- 🏢 **Real Buildings**: {real_buildings}/{total_neighbors}")
                st.markdown(f"- ☀️ **Existing Solar**: {existing_solar}/{total_neighbors}")
                st.markdown(f"- 📊 **Simulated**: {total_neighbors - real_buildings}/{total_neighbors}")
            else:
                st.markdown("---")
                st.markdown("**⚠️ All data is simulated** - Real building data not available for this area")
            
            # Best candidate
            st.markdown("---")
            st.subheader("🥇 Best Candidate")
            top_home = neighbors.nlargest(1, 'yearly_energy_kwh').iloc[0]
            st.success(f"**{top_home['address']}**  \n{top_home['yearly_energy_kwh']:,} kWh/year  \nRoof: {top_home['roof_orientation']} facing")
    
    with tab2:
        st.subheader("📊 Solar Potential Analytics")
        
        neighbors = df[df['type'] != 'center']
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Potential distribution
            potential_counts = neighbors['potential_category'].value_counts()
            
            fig_pie = go.Figure(data=[go.Pie(
                labels=potential_counts.index,
                values=potential_counts.values,
                hole=0.4,
                marker_colors=['#00ff00', '#90EE90', '#FFA500', '#FF6B6B'],
                textinfo='label+percent'
            )])
            
            fig_pie.update_layout(
                title="Solar Potential Distribution",
                height=400,
                annotations=[dict(text='Homes', x=0.5, y=0.5, font_size=20, showarrow=False)]
            )
            
            st.plotly_chart(fig_pie, use_container_width=True)
            
            # Roof orientation analysis
            st.subheader("🧭 Roof Orientation Impact")
            orientation_data = neighbors.groupby('roof_orientation')['yearly_energy_kwh'].mean().sort_values(ascending=False)
            
            fig_orientation = px.bar(
                x=orientation_data.index,
                y=orientation_data.values,
                labels={'x': 'Roof Orientation', 'y': 'Avg. Energy (kWh/yr)'},
                color=orientation_data.values,
                color_continuous_scale='Viridis'
            )
            st.plotly_chart(fig_orientation, use_container_width=True)
        
        with col2:
            # Energy potential by home
            sorted_neighbors = neighbors.sort_values('yearly_energy_kwh', ascending=False)
            
            fig_bar = px.bar(
                sorted_neighbors,
                x='yearly_energy_kwh',
                y='address',
                orientation='h',
                color='potential_category',
                color_discrete_map={
                    'Excellent': '#00ff00',
                    'Good': '#90EE90',
                    'Moderate': '#FFA500',
                    'Low': '#FF6B6B'
                },
                labels={'yearly_energy_kwh': 'Annual Energy (kWh)', 'address': 'Address'},
                title="Energy Generation Potential by Home"
            )
            
            fig_bar.update_layout(height=600, showlegend=False)
            st.plotly_chart(fig_bar, use_container_width=True)
        
        # Impact metrics
        st.subheader("🌟 Neighborhood Impact Analysis")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            total_energy = neighbors['yearly_energy_kwh'].sum()
            st.info(f"""
            **⚡ Total Energy**  
            {total_energy:,} kWh/yr  
            Powers ~{total_energy/11000:.0f} homes
            """)
        
        with col2:
            total_carbon = neighbors['carbon_offset_kg'].sum()/1000
            st.success(f"""
            **🌱 CO₂ Reduction**  
            {total_carbon:.1f} tons/yr  
            = {total_carbon*24:.0f} trees
            """)
        
        with col3:
            total_panels = neighbors['panel_capacity_kw'].sum()
            st.warning(f"""
            **☀️ Solar Capacity**  
            {total_panels:.1f} kW  
            ~{total_panels*4:.0f} panels
            """)
        
        with col4:
            avg_roi = 7.5  # years average
            st.error(f"""
            **💰 Avg. ROI**  
            {avg_roi} years  
            25 yr savings: ${total_energy*0.15*25:,.0f}
            """)
    
    with tab3:
        st.subheader("📋 Detailed Solar Analysis Data")

        # Data source information
        real_data_count = len(df[df.get('data_source', '') == 'Google Solar API'])
        simulated_data_count = len(df[df.get('data_source', '') == 'Simulated'])

        if real_data_count > 0:
            st.info(f"🎯 **Data Quality**: {real_data_count} properties with real Google Solar API data, {simulated_data_count} with simulated data")
        else:
            st.warning("⚠️ **Data Quality**: All data is simulated. Real solar data may not be available for this location.")
        
        # Prepare display dataframe
        display_df = df.copy()
        
        # Select and order columns
        display_columns = [
            'address', 'direction', 'potential_category', 'yearly_energy_kwh',
            'carbon_offset_kg', 'roof_area_sqm', 'roof_orientation',
            'shading_factor', 'roof_age', 'data_source'
        ]
        
        # Filter columns that exist
        available_columns = [col for col in display_columns if col in display_df.columns]
        display_df = display_df[available_columns]
        
        # Enhanced style function with more vibrant colors
        def style_row(row):
            if 'potential_category' in row:
                if row['potential_category'] == 'Existing Solar':
                    return ['background-color: #b3d9ff; color: #003d7a; font-weight: bold'] * len(row)
                elif row['potential_category'] == 'Excellent':
                    return ['background-color: #a8e6a3; color: #1b5e20; font-weight: bold'] * len(row)
                elif row['potential_category'] == 'Good':
                    return ['background-color: #fff176; color: #f57f17; font-weight: bold'] * len(row)
                elif row['potential_category'] == 'Moderate':
                    return ['background-color: #ffcc80; color: #e65100; font-weight: bold'] * len(row)
                else:  # Low
                    return ['background-color: #ffab91; color: #bf360c; font-weight: bold'] * len(row)
            return [''] * len(row)
        
        st.dataframe(
            display_df.style.apply(style_row, axis=1),
            use_container_width=True,
            height=500
        )
        
        # Summary stats
        st.subheader("📊 Statistical Summary")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("**Top 3 Homes by Potential**")
            top3 = neighbors.nlargest(3, 'yearly_energy_kwh')[['address', 'yearly_energy_kwh']]
            st.dataframe(top3)
        
        with col2:
            st.markdown("**Potential by Orientation**")
            orientation_summary = neighbors.groupby('roof_orientation')['yearly_energy_kwh'].agg(['mean', 'count'])
            st.dataframe(orientation_summary)
        
        with col3:
            st.markdown("**Category Distribution**")
            category_summary = neighbors['potential_category'].value_counts()
            st.dataframe(category_summary)
    with tab4:
        st.subheader("💾 Export Results & Summary")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("### 🤖 AI-Generated Executive Summary")
            
            # Calculate summary statistics
            neighbors = df[df['type'] != 'center']
            excellent = len(neighbors[neighbors['potential_category'] == 'Excellent'])
            good = len(neighbors[neighbors['potential_category'] == 'Good'])
            total_energy = neighbors['yearly_energy_kwh'].sum()
            total_carbon = neighbors['carbon_offset_kg'].sum()/1000
            best_homes = neighbors.nlargest(3, 'yearly_energy_kwh')
            
            summary = f"""
            📊 **Solar Neighborhood Analysis Report**
            
            **Location:** {st.session_state.get('location_data', {}).get('center', {}).get('address', 'Unknown Location')}
            **Analysis Date:** {datetime.now().strftime('%B %d, %Y')}  
            **Homes Analyzed:** {len(neighbors)} properties within {analysis_radius}m
            
            **🎯 Key Findings:**
            
            This comprehensive analysis of the {location} neighborhood reveals significant 
            solar adoption potential. Of the {len(neighbors)} homes analyzed:
            
            • **{excellent}** properties show EXCELLENT solar potential (>6,000 kWh/year)
            • **{good}** properties show GOOD potential (4,000-6,000 kWh/year)
            • **{len(neighbors) - excellent - good}** properties have moderate to low potential
            
            **💡 Top 3 Solar Candidates:**
            """
            
            for idx, (_, home) in enumerate(best_homes.iterrows(), 1):
                summary += f"""
            {idx}. **{home['address']}**
               - Annual Generation: {home['yearly_energy_kwh']:,} kWh
               - CO₂ Offset: {home['carbon_offset_kg']/1000:.1f} tons/year
               - Roof: {home['roof_area_sqm']}m², facing {home.get('roof_orientation', 'N/A')}
            """
            
            summary += f"""
            
            **🌍 Environmental Impact:**
            If all viable homes ({excellent + good} properties) install solar:
            - Total Energy Generation: **{total_energy:,.0f} kWh/year**
            - Carbon Offset: **{total_carbon:.1f} tons CO₂/year**
            - Equivalent to planting **{int(total_carbon * 24)} trees annually**
            
            **💰 Economic Benefits:**
            - Estimated Annual Savings: **${total_energy * 0.15:,.0f}**
            - 25-Year Savings: **${total_energy * 0.15 * 25:,.0f}**
            - Average Payback Period: **7-8 years**
            
            **📈 Recommendations:**
            1. **Immediate Action:** Contact the {excellent} excellent-rated homes first
            2. **Community Approach:** Organize a neighborhood solar information session
            3. **Group Buying:** Leverage bulk purchasing for 10-15% cost reduction
            4. **Success Story:** Use the existing solar home as a case study
            
            **🔮 Next Steps:**
            - Schedule consultations with top candidates
            - Arrange site assessments for shading analysis
            - Explore local incentives and tax credits
            - Consider community solar options for lower-potential homes
            
            ---
            *Analysis based on roof characteristics, orientation, shading, and local solar irradiance data*
            """
            
            st.info(summary)
            
            # Download summary
            st.download_button(
                label="📄 Download Summary (TXT)",
                data=summary,
                file_name=f"solar_analysis_{location.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d')}.txt",
                mime="text/plain"
            )
        
        with col2:
            st.markdown("### 📥 Data Export Options")
            
            # Prepare full export data
            export_df = df.copy()
            export_df['analysis_date'] = datetime.now().strftime('%Y-%m-%d')
            export_df['analysis_location'] = location
            export_df['analysis_radius_m'] = analysis_radius
            
            # CSV export
            csv = export_df.to_csv(index=False)
            st.download_button(
                label="📊 Download Full Data (CSV)",
                data=csv,
                file_name=f"solar_data_{location.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d')}.csv",
                mime="text/csv"
            )
            
            # Create professional HTML report
            html_report = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Solar Neighborhood Analysis - {location}</title>
                <style>
                    body {{ 
                        font-family: 'Segoe UI', Arial, sans-serif; 
                        margin: 40px;
                        color: #333;
                    }}
                    h1 {{ 
                        color: #FF6B6B; 
                        border-bottom: 3px solid #FF6B6B;
                        padding-bottom: 10px;
                    }}
                    h2 {{ 
                        color: #333; 
                        margin-top: 30px;
                    }}
                    table {{ 
                        border-collapse: collapse; 
                        width: 100%; 
                        margin: 20px 0;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }}
                    th, td {{ 
                        border: 1px solid #ddd; 
                        padding: 12px; 
                        text-align: left; 
                    }}
                    th {{ 
                        background-color: #FF6B6B; 
                        color: white;
                        font-weight: bold;
                    }}
                    tr:nth-child(even) {{ background-color: #f9f9f9; }}
                    tr:hover {{ background-color: #f5f5f5; }}
                    .excellent {{ background-color: #a8e6a3 !important; color: #1b5e20 !important; font-weight: bold !important; }}
                    .good {{ background-color: #fff176 !important; color: #f57f17 !important; font-weight: bold !important; }}
                    .moderate {{ background-color: #ffcc80 !important; color: #e65100 !important; font-weight: bold !important; }}
                    .low {{ background-color: #ffab91 !important; color: #bf360c !important; font-weight: bold !important; }}
                    .existingsolar {{ background-color: #b3d9ff !important; color: #003d7a !important; font-weight: bold !important; }}
                    .metric-box {{
                        display: inline-block;
                        padding: 20px;
                        margin: 10px;
                        border-radius: 10px;
                        background-color: #f0f2f6;
                        text-align: center;
                        min-width: 150px;
                    }}
                    .metric-value {{
                        font-size: 2em;
                        font-weight: bold;
                        color: #FF6B6B;
                    }}
                    .metric-label {{
                        font-size: 0.9em;
                        color: #666;
                        margin-top: 5px;
                    }}
                </style>
            </head>
            <body>
                <h1>☀️ Solar Neighborhood Analysis Report</h1>
                <p><strong>Location:</strong> {st.session_state.get('location_data', {}).get('center', {}).get('address', 'Unknown Location')}</p>
                <p><strong>Report Date:</strong> {datetime.now().strftime('%B %d, %Y at %I:%M %p')}</p>
                <p><strong>Analysis Radius:</strong> {analysis_radius} meters</p>
                
                <h2>📊 Summary Metrics</h2>
                <div>
                    <div class="metric-box">
                        <div class="metric-value">{len(neighbors)}</div>
                        <div class="metric-label">Homes Analyzed</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value">{excellent + good}</div>
                        <div class="metric-label">High Potential</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value">{total_energy/1000:.0f}</div>
                        <div class="metric-label">MWh/Year Potential</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value">{total_carbon:.0f}</div>
                        <div class="metric-label">Tons CO₂ Offset</div>
                    </div>
                </div>
                
                <h2>🏠 Detailed Home Analysis</h2>
                <table>
                    <tr>
                        <th>Address</th>
                        <th>Direction</th>
                        <th>Potential</th>
                        <th>Annual Energy (kWh)</th>
                        <th>CO₂ Offset (tons/yr)</th>
                        <th>Roof Area (m²)</th>
                        <th>Orientation</th>
                        <th>Shading</th>
                    </tr>
            """
            
            for _, home in df.iterrows():
                row_class = home['potential_category'].lower().replace(' ', '')
                html_report += f"""
                    <tr class="{row_class}">
                        <td>{home['address']}</td>
                        <td>{home.get('direction', 'Center')}</td>
                        <td><strong>{home['potential_category']}</strong></td>
                        <td>{home['yearly_energy_kwh']:,}</td>
                        <td>{home['carbon_offset_kg']/1000:.1f}</td>
                        <td>{home['roof_area_sqm']}</td>
                        <td>{home.get('roof_orientation', 'N/A')}</td>
                        <td>{home.get('shading_factor', 0):.0%}</td>
                    </tr>
                """
            
            html_report += """
                </table>
                
                <h2>💡 Recommendations</h2>
                <ol>
                    <li>Begin with homes rated "Excellent" for highest impact</li>
                    <li>Organize a community information session</li>
                    <li>Explore group purchasing options</li>
                    <li>Investigate local and federal incentives</li>
                    <li>Consider battery storage for enhanced benefits</li>
                </ol>
                
                <p style="margin-top: 40px; text-align: center; color: #666;">
                    Generated by SunNeighbor - Know where solar belongs — next door | Powered by Google Maps & AI
                </p>
            </body>
            </html>
            """
            
            st.download_button(
                label="📑 Download Report (HTML)",
                data=html_report,
                file_name=f"solar_report_{location.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d')}.html",
                mime="text/html"
            )
            
            # Quick metrics
            st.markdown("### 📈 Quick Metrics")
            
            metric1, metric2 = st.columns(2)
            with metric1:
                st.metric("Best Home", f"{best_homes.iloc[0]['yearly_energy_kwh']:,} kWh/yr")
            with metric2:
                st.metric("Avg Potential", f"{neighbors['yearly_energy_kwh'].mean():,.0f} kWh/yr")
            
            # Share options
            st.markdown("### 🔗 Share Results")
            
            share_text = f"""🌟 Solar Analysis Complete for {location}!
Found {excellent + good} high-potential homes out of {len(neighbors)} analyzed.
Total potential: {total_energy:,.0f} kWh/year
Carbon offset: {total_carbon:.1f} tons CO₂/year
#SolarEnergy #Sustainability #CleanEnergy"""
            
            st.text_area("Copy this text to share:", share_text, height=100)
            
            # ROI Calculator
            st.markdown("### 💰 Quick ROI Calculator")
            
            system_cost = st.number_input(
                "Average system cost ($)",
                min_value=10000,
                max_value=50000,
                value=20000,
                step=1000
            )
            
            electricity_rate = st.number_input(
                "Electricity rate ($/kWh)",
                min_value=0.05,
                max_value=0.50,
                value=0.15,
                step=0.01
            )
            
            if st.button("Calculate ROI"):
                avg_energy = neighbors['yearly_energy_kwh'].mean()
                annual_savings = avg_energy * electricity_rate
                payback_years = system_cost / annual_savings
                total_savings = annual_savings * 25 - system_cost
                
                st.success(f"""
                **ROI Analysis (Average Home):**
                - Annual Savings: ${annual_savings:,.0f}
                - Payback Period: {payback_years:.1f} years
                - 25-Year Net Savings: ${total_savings:,.0f}
                - ROI: {(total_savings / system_cost * 100):.0f}%
                """)

# Footer
st.markdown("---")
st.markdown(
    """
    <div style='text-align: center; color: #666; padding: 20px;'>
        <p>☀️ SunNeighbor - Know where solar belongs — next door | Real Addresses, Real Potential | 
        Built for a Sustainable Future</p>
        <p style='font-size: 0.9em;'>Data accuracy depends on roof conditions, local weather patterns, and shading analysis</p>
    </div>
    """,
    unsafe_allow_html=True
)

# Add session state persistence for better UX
if 'analyzed_locations' not in st.session_state:
    st.session_state.analyzed_locations = []

if analyze_btn and location not in st.session_state.analyzed_locations:
    st.session_state.analyzed_locations.append(location)

# Optional: Add comparison feature
if len(st.session_state.analyzed_locations) > 1:
    with st.expander("📊 Compare Previous Analyses"):
        st.write("Previously analyzed locations:")
        for loc in st.session_state.analyzed_locations:
            st.write(f"- {loc}")